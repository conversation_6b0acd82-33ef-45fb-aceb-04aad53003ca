#!/bin/bash

# 构建前端并启动服务
# 解决前端缺少生产构建的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "=== 构建并启动DeerFlow前端 ==="
echo ""

# 获取当前工作目录
WORK_DIR=$(pwd)

log_step "停止前端服务..."
sudo systemctl stop deer-flow-frontend 2>/dev/null || true

log_step "检查前端目录..."
if [ ! -d "web" ]; then
    log_error "未找到web目录"
    exit 1
fi

cd web

log_step "检查package.json..."
if [ ! -f "package.json" ]; then
    log_error "未找到package.json文件"
    exit 1
fi

log_step "安装前端依赖..."
pnpm install

log_step "构建前端应用..."
# 设置环境变量
export NODE_ENV=production
export SKIP_ENV_VALIDATION=1

# 构建应用
pnpm build

log_step "检查构建结果..."
if [ -d ".next" ]; then
    log_info "前端构建成功"
    ls -la .next/
else
    log_error "前端构建失败，未找到.next目录"
    exit 1
fi

# 返回项目根目录
cd "$WORK_DIR"

log_step "启动前端服务..."
sudo systemctl start deer-flow-frontend

# 等待前端启动
sleep 10

log_step "检查前端服务状态..."
if sudo systemctl is-active --quiet deer-flow-frontend; then
    log_info "前端服务启动成功"
else
    log_warn "前端服务启动失败，查看详细日志..."
    sudo journalctl -u deer-flow-frontend -n 20 --no-pager
fi

log_step "检查端口监听状态..."
echo "当前端口监听状态:"
netstat -tuln | grep -E ':(8000|3000)'

log_step "测试服务连接..."
sleep 5

# 测试后端连接
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs | grep -q 200; then
    log_info "后端服务正常"
else
    log_warn "后端服务异常"
fi

# 测试前端连接
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q 200; then
    log_info "前端服务正常"
else
    log_warn "前端服务异常"
fi

# 获取服务器IP
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || hostname -I | awk '{print $1}')

echo ""
log_step "构建和启动完成！"
echo ""

echo "=== 服务状态 ==="
echo "后端: $(sudo systemctl is-active deer-flow-backend)"
echo "前端: $(sudo systemctl is-active deer-flow-frontend)"
echo ""

echo "=== 访问信息 ==="
if [ -n "$SERVER_IP" ]; then
    echo "前端地址: http://$SERVER_IP:3000"
    echo "后端API: http://$SERVER_IP:8000/api"
    echo "API文档: http://$SERVER_IP:8000/docs"
else
    echo "前端地址: http://YOUR_SERVER_IP:3000"
    echo "后端API: http://YOUR_SERVER_IP:8000/api"
    echo "API文档: http://YOUR_SERVER_IP:8000/docs"
fi
echo ""

echo "=== 测试前端API连接 ==="
if [ -n "$SERVER_IP" ]; then
    echo "测试外部连接..."
    if curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:8000/docs | grep -q 200; then
        log_info "外部后端连接正常"
    else
        log_warn "外部后端连接失败，请检查防火墙设置"
    fi
    
    if curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:3000 | grep -q 200; then
        log_info "外部前端连接正常"
    else
        log_warn "外部前端连接失败，请检查防火墙设置"
    fi
fi

echo ""
echo "=== 管理命令 ==="
echo "查看前端日志: sudo journalctl -u deer-flow-frontend -f"
echo "查看后端日志: sudo journalctl -u deer-flow-backend -f"
echo "重启前端: sudo systemctl restart deer-flow-frontend"
echo "重启后端: sudo systemctl restart deer-flow-backend"
echo ""

echo "如果前端仍有问题，可以手动测试:"
echo "cd web && pnpm start"
