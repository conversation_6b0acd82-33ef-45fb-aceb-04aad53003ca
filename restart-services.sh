#!/bin/bash

# 快速重启DeerFlow服务的脚本
# 确保使用正确的host绑定

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "=== 重启DeerFlow服务 ==="
echo ""

log_step "停止现有服务..."
sudo systemctl stop deer-flow-backend deer-flow-frontend 2>/dev/null || true

# 强制杀死可能残留的进程
pkill -f "server.py" 2>/dev/null || true
pkill -f "uv run server.py" 2>/dev/null || true
pkill -f "pnpm" 2>/dev/null || true
sleep 3

log_step "启动后端服务..."
sudo systemctl start deer-flow-backend

# 等待后端启动
sleep 8

log_step "检查后端服务..."
if sudo systemctl is-active --quiet deer-flow-backend; then
    log_info "后端服务运行正常"
else
    log_error "后端服务启动失败"
    sudo journalctl -u deer-flow-backend -n 10 --no-pager
    exit 1
fi

log_step "启动前端服务..."
sudo systemctl start deer-flow-frontend

# 等待前端启动
sleep 8

log_step "检查前端服务..."
if sudo systemctl is-active --quiet deer-flow-frontend; then
    log_info "前端服务运行正常"
else
    log_warn "前端服务可能有问题"
    sudo journalctl -u deer-flow-frontend -n 10 --no-pager
fi

log_step "检查端口绑定..."
echo "当前端口监听状态:"
netstat -tuln | grep -E ':(8000|3000)'

log_step "测试连接..."
sleep 5

# 测试后端连接
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs | grep -q 200; then
    log_info "后端API连接正常"
else
    log_error "后端API连接失败"
fi

# 测试前端连接
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q 200; then
    log_info "前端连接正常"
else
    log_warn "前端连接可能有问题"
fi

# 获取服务器IP并显示访问信息
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || hostname -I | awk '{print $1}')

echo ""
log_step "重启完成！"
echo ""
echo "=== 访问信息 ==="
if [ -n "$SERVER_IP" ]; then
    echo "前端地址: http://$SERVER_IP:3000"
    echo "后端API: http://$SERVER_IP:8000/api"
    echo "API文档: http://$SERVER_IP:8000/docs"
else
    echo "前端地址: http://YOUR_SERVER_IP:3000"
    echo "后端API: http://YOUR_SERVER_IP:8000/api"
    echo "API文档: http://YOUR_SERVER_IP:8000/docs"
fi
echo ""

echo "=== 服务状态 ==="
echo "后端: $(sudo systemctl is-active deer-flow-backend)"
echo "前端: $(sudo systemctl is-active deer-flow-frontend)"
echo ""

echo "如需查看日志，请运行:"
echo "sudo journalctl -u deer-flow-backend -f"
echo "sudo journalctl -u deer-flow-frontend -f"
