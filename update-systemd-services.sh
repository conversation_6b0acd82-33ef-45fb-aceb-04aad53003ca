#!/bin/bash

# 更新systemd服务配置，确保后端绑定到0.0.0.0
# 解决后端只绑定到127.0.0.1的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "=== 更新DeerFlow systemd服务配置 ==="
echo ""

# 获取当前用户和工作目录
CURRENT_USER=$(whoami)
WORK_DIR=$(pwd)

log_step "停止现有服务..."
sudo systemctl stop deer-flow-backend deer-flow-frontend 2>/dev/null || true

# 杀死可能残留的进程
pkill -f "server.py" 2>/dev/null || true
pkill -f "uv run server.py" 2>/dev/null || true
sleep 2

log_step "更新后端服务配置..."
# 创建后端服务文件，明确指定--host 0.0.0.0
sudo tee /etc/systemd/system/deer-flow-backend.service > /dev/null <<EOF
[Unit]
Description=DeerFlow Backend Service
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$WORK_DIR
Environment=PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/home/<USER>/.local/bin/uv run server.py --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

log_step "更新前端服务配置..."
# 创建前端服务文件
sudo tee /etc/systemd/system/deer-flow-frontend.service > /dev/null <<EOF
[Unit]
Description=DeerFlow Frontend Service
After=network.target deer-flow-backend.service

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$WORK_DIR/web
Environment=PATH=/usr/bin:/usr/local/bin:/bin
Environment=NODE_ENV=production
ExecStart=/usr/bin/pnpm start
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

log_step "重新加载systemd配置..."
sudo systemctl daemon-reload

log_step "启用服务..."
sudo systemctl enable deer-flow-backend
sudo systemctl enable deer-flow-frontend

log_step "启动后端服务..."
sudo systemctl start deer-flow-backend

# 等待后端启动
sleep 5

log_step "检查后端服务状态..."
if sudo systemctl is-active --quiet deer-flow-backend; then
    log_info "后端服务启动成功"
else
    log_error "后端服务启动失败"
    sudo systemctl status deer-flow-backend --no-pager -l
    exit 1
fi

log_step "启动前端服务..."
sudo systemctl start deer-flow-frontend

# 等待前端启动
sleep 5

log_step "检查前端服务状态..."
if sudo systemctl is-active --quiet deer-flow-frontend; then
    log_info "前端服务启动成功"
else
    log_error "前端服务启动失败"
    sudo systemctl status deer-flow-frontend --no-pager -l
fi

log_step "检查端口绑定状态..."
echo "当前端口监听状态:"
netstat -tuln | grep -E ':(8000|3000)' || echo "未发现服务监听"

log_step "测试服务连接..."
sleep 5

# 测试本地连接
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs | grep -q 200; then
    log_info "后端服务连接正常"
else
    log_warn "后端服务连接失败，正在检查..."
    curl -v http://localhost:8000/docs || true
fi

# 获取服务器IP
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || hostname -I | awk '{print $1}')

if [ -n "$SERVER_IP" ] && [ "$SERVER_IP" != "127.0.0.1" ]; then
    echo ""
    log_step "测试外部连接 (IP: $SERVER_IP)..."
    if curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:8000/docs | grep -q 200; then
        log_info "外部后端连接正常"
    else
        log_warn "外部后端连接失败，请检查防火墙和安全组设置"
    fi
fi

echo ""
log_step "服务配置完成！"
echo ""
echo "=== 服务状态 ==="
sudo systemctl status deer-flow-backend --no-pager -l | head -5
echo ""
sudo systemctl status deer-flow-frontend --no-pager -l | head -5
echo ""

echo "=== 访问信息 ==="
if [ -n "$SERVER_IP" ]; then
    echo "前端地址: http://$SERVER_IP:3000"
    echo "后端API: http://$SERVER_IP:8000/api"
    echo "API文档: http://$SERVER_IP:8000/docs"
else
    echo "前端地址: http://YOUR_SERVER_IP:3000"
    echo "后端API: http://YOUR_SERVER_IP:8000/api"
    echo "API文档: http://YOUR_SERVER_IP:8000/docs"
fi
echo ""

echo "=== 管理命令 ==="
echo "查看后端日志: sudo journalctl -u deer-flow-backend -f"
echo "查看前端日志: sudo journalctl -u deer-flow-frontend -f"
echo "重启服务: sudo systemctl restart deer-flow-backend deer-flow-frontend"
echo ""
echo "=== 配置完成 ==="
