#!/bin/bash

# 修复systemd服务路径问题
# 检查并修复uv和其他工具的路径

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "=== 修复DeerFlow systemd服务路径问题 ==="
echo ""

# 获取当前用户和工作目录
CURRENT_USER=$(whoami)
WORK_DIR=$(pwd)

log_step "检查工具路径..."

# 检查uv路径
UV_PATH=""
for path in "/home/<USER>/.local/bin/uv" "/usr/local/bin/uv" "/usr/bin/uv" "/root/.local/bin/uv" "$(command -v uv 2>/dev/null)"; do
    if [ -x "$path" ]; then
        UV_PATH="$path"
        log_info "找到uv: $UV_PATH"
        break
    fi
done

if [ -z "$UV_PATH" ]; then
    log_error "未找到uv，正在尝试安装..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source ~/.bashrc
    UV_PATH="/home/<USER>/.local/bin/uv"
    if [ ! -x "$UV_PATH" ]; then
        UV_PATH="/root/.local/bin/uv"
    fi
fi

# 检查pnpm路径
PNPM_PATH=""
for path in "/usr/bin/pnpm" "/usr/local/bin/pnpm" "/home/<USER>/.local/bin/pnpm" "$(command -v pnpm 2>/dev/null)"; do
    if [ -x "$path" ]; then
        PNPM_PATH="$path"
        log_info "找到pnpm: $PNPM_PATH"
        break
    fi
done

if [ -z "$PNPM_PATH" ]; then
    log_error "未找到pnpm，正在尝试安装..."
    npm install -g pnpm
    PNPM_PATH="/usr/bin/pnpm"
fi

# 检查Python路径
PYTHON_PATH=""
for path in "/usr/bin/python3" "/usr/local/bin/python3" "/bin/python3" "$(command -v python3 2>/dev/null)"; do
    if [ -x "$path" ]; then
        PYTHON_PATH="$path"
        log_info "找到Python: $PYTHON_PATH"
        break
    fi
done

log_step "停止现有服务..."
sudo systemctl stop deer-flow-backend deer-flow-frontend 2>/dev/null || true

log_step "创建修复后的后端服务配置..."
# 创建后端服务文件，使用找到的正确路径
sudo tee /etc/systemd/system/deer-flow-backend.service > /dev/null <<EOF
[Unit]
Description=DeerFlow Backend Service
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$WORK_DIR
Environment=PATH=/usr/local/bin:/usr/bin:/bin:/home/<USER>/.local/bin:/root/.local/bin
Environment=PYTHONPATH=$WORK_DIR
ExecStart=$UV_PATH run server.py --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
EOF

log_step "创建修复后的前端服务配置..."
# 创建前端服务文件
sudo tee /etc/systemd/system/deer-flow-frontend.service > /dev/null <<EOF
[Unit]
Description=DeerFlow Frontend Service
After=network.target deer-flow-backend.service

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$WORK_DIR/web
Environment=PATH=/usr/local/bin:/usr/bin:/bin:/home/<USER>/.local/bin
Environment=NODE_ENV=production
ExecStart=$PNPM_PATH start
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
EOF

log_step "重新加载systemd配置..."
sudo systemctl daemon-reload

log_step "测试uv命令..."
if $UV_PATH --version; then
    log_info "uv命令测试成功"
else
    log_error "uv命令测试失败"
    exit 1
fi

log_step "测试Python环境..."
cd "$WORK_DIR"
if $UV_PATH run python --version; then
    log_info "Python环境测试成功"
else
    log_error "Python环境测试失败"
    exit 1
fi

log_step "启动后端服务..."
sudo systemctl start deer-flow-backend

# 等待服务启动
sleep 10

log_step "检查后端服务状态..."
if sudo systemctl is-active --quiet deer-flow-backend; then
    log_info "后端服务启动成功"
else
    log_error "后端服务仍然启动失败"
    echo "详细错误信息:"
    sudo journalctl -u deer-flow-backend -n 20 --no-pager
    echo ""
    echo "服务状态:"
    sudo systemctl status deer-flow-backend --no-pager -l
    exit 1
fi

log_step "检查端口绑定..."
sleep 5
echo "当前端口监听状态:"
netstat -tuln | grep -E ':(8000|3000)' || echo "未发现服务监听"

log_step "测试后端连接..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs | grep -q 200; then
    log_info "后端连接测试成功"
else
    log_warn "后端连接测试失败，正在检查..."
    curl -v http://localhost:8000/docs 2>&1 | head -10 || true
fi

log_step "启动前端服务..."
sudo systemctl start deer-flow-frontend

# 等待前端启动
sleep 10

log_step "检查前端服务状态..."
if sudo systemctl is-active --quiet deer-flow-frontend; then
    log_info "前端服务启动成功"
else
    log_warn "前端服务启动失败"
    sudo journalctl -u deer-flow-frontend -n 10 --no-pager
fi

# 获取服务器IP
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || hostname -I | awk '{print $1}')

echo ""
log_step "修复完成！"
echo ""
echo "=== 使用的路径 ==="
echo "uv路径: $UV_PATH"
echo "pnpm路径: $PNPM_PATH"
echo "工作目录: $WORK_DIR"
echo ""

echo "=== 服务状态 ==="
echo "后端: $(sudo systemctl is-active deer-flow-backend)"
echo "前端: $(sudo systemctl is-active deer-flow-frontend)"
echo ""

echo "=== 访问信息 ==="
if [ -n "$SERVER_IP" ]; then
    echo "前端地址: http://$SERVER_IP:3000"
    echo "后端API: http://$SERVER_IP:8000/api"
    echo "API文档: http://$SERVER_IP:8000/docs"
else
    echo "前端地址: http://YOUR_SERVER_IP:3000"
    echo "后端API: http://YOUR_SERVER_IP:8000/api"
    echo "API文档: http://YOUR_SERVER_IP:8000/docs"
fi
echo ""

echo "=== 管理命令 ==="
echo "查看后端日志: sudo journalctl -u deer-flow-backend -f"
echo "查看前端日志: sudo journalctl -u deer-flow-frontend -f"
echo "重启服务: sudo systemctl restart deer-flow-backend deer-flow-frontend"
